==> 2025-06-30 21:41:59 <==
# cmd: C:\Users\<USER>\anaconda3\Scripts\conda-script.py create -p venv python==3.10 -y
# conda version: 23.1.0
+defaults/noarch::pip-25.1-pyhc872135_2
+defaults/noarch::tzdata-2025b-h04d1e81_0
+defaults/win-64::bzip2-1.0.8-h2bbff1b_6
+defaults/win-64::ca-certificates-2025.2.25-haa95532_0
+defaults/win-64::libffi-3.4.4-hd77b12b_1
+defaults/win-64::openssl-1.1.1w-h2bbff1b_0
+defaults/win-64::python-3.10.0-h96c0403_3
+defaults/win-64::setuptools-78.1.1-py310haa95532_0
+defaults/win-64::sqlite-3.45.3-h2bbff1b_0
+defaults/win-64::tk-8.6.14-h5e9d12e_1
+defaults/win-64::vc-14.42-haa95532_5
+defaults/win-64::vs2015_runtime-14.42.34433-hbfb602d_5
+defaults/win-64::wheel-0.45.1-py310haa95532_0
+defaults/win-64::xz-5.6.4-h4754444_1
+defaults/win-64::zlib-1.2.13-h8cc25b3_1
# update specs: ['python==3.10']
